{"name": "saygo", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android --device", "ios": "expo run:ios --device", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@mapbox/polyline": "^1.2.1", "@maplibre/maplibre-react-native": "^10.0.0-alpha.5", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-community/datetimepicker": "^8.3.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@reduxjs/toolkit": "^2.7.0", "axios": "^1.9.0", "expo": "~52.0.46", "expo-blur": "~14.0.3", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "~15.0.2", "expo-linear-gradient": "^14.0.2", "expo-linking": "~7.0.5", "expo-location": "^18.0.10", "expo-router": "~4.0.20", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "form-data": "^4.0.2", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "^13.12.5", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/mapbox__polyline": "^1.0.5", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-native-dotenv": "^3.4.11", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}